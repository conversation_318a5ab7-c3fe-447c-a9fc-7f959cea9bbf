#!/usr/bin/env node

/**
 * Test script to verify plan generation logging
 * Run with: node test-plan-logging.js
 */

require('dotenv').config();

console.log('🧪 Testing Plan Generation Logging');
console.log('==================================');

// Check environment variables
console.log('\n📋 Environment Check:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('LITELLM_BASE_URL:', process.env.LITELLM_BASE_URL);
console.log('LITELLM_API_KEY available:', !!process.env.LITELLM_API_KEY);
console.log('DEEPSEEK_API_KEY available:', !!process.env.DEEPSEEK_API_KEY);

// Test the LLM service
async function testPlanGeneration() {
  try {
    console.log('\n🔧 Loading LLM Service...');
    const LLMServiceV3 = require('./services/llmServiceV3');
    const llmService = new LLMServiceV3();
    
    console.log('\n🎯 Testing getBestProvider...');
    const provider = llmService.getBestProvider();
    console.log('Selected provider:', provider);
    
    console.log('\n🔧 Testing createLLM...');
    const llm = llmService.createLLM(null, false, 'planning');
    console.log('LLM instance created successfully');
    
    console.log('\n📋 Testing structured plan generation...');
    const result = await llmService.generateStructuredPlan('Create a simple todo app', 'desktop');
    
    console.log('\n✅ Plan generation result:');
    console.log('Success:', result.success);
    if (result.success) {
      console.log('Plan keys:', Object.keys(result.plan));
    } else {
      console.log('Error:', result.error);
      if (result.rawResponse) {
        console.log('Raw response preview:', result.rawResponse.substring(0, 200) + '...');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testPlanGeneration().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test crashed:', error);
  process.exit(1);
});
