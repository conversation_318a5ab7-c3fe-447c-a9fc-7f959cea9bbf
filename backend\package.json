{"name": "just-prototype-backend", "version": "1.0.0", "description": "Backend for Just Prototype", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:critical": "node tests/runCriticalTests.js", "test:fixes": "npx jest tests/editingFixes.test.js --verbose --no-coverage", "test:fragment": "jest tests/llmServiceV3.test.js", "test:diff": "jest tests/diffService.test.js", "test:api": "jest tests/htmlEditing.test.js", "test:all": "jest --coverage --verbose"}, "dependencies": {"@anthropic-ai/sdk": "^0.4.3", "@babel/parser": "^7.21.3", "@dqbd/tiktoken": "^1.0.21", "@langchain/anthropic": "^0.1.1", "@langchain/community": "^0.0.26", "@langchain/core": "^0.1.1", "@langchain/openai": "^0.0.14", "@typescript-eslint/parser": "^5.57.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "body-parser": "^1.20.2", "cheerio": "^1.1.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "css-tree": "^2.3.1", "diff-match-patch": "^1.0.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "langchain": "^0.1.18", "morgan": "^1.10.0", "node-html-parser": "^6.1.5", "openai": "^4.27.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.10.0", "prettier": "^2.8.7", "recast": "^0.22.0", "sanitize-html": "^2.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "babel-jest": "^30.0.2", "jest": "^29.7.0", "jsdom": "^26.1.0", "nodemon": "^2.0.22", "supertest": "^6.3.4"}}