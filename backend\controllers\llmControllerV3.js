const llmServiceV3 = require('../services/llmServiceV3');
const versionService = require('../services/versionService');

/**
 * V3 LLM Controller - Clean implementation based on Readdy.ai approach
 * Focuses on accurate, targeted editing through sophisticated prompting
 * Enhanced with prototyping robustness and validation
 */



// Removed duplicate generateIntent function - see line 188 for the actual implementation

/**
 * Implement feature functionality (inline/modal/page) with server-side prompts
 */
async function implementFeature(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, elementText, elementType, implementationType, conversationHistory, intentData } = req.body;
    if (!htmlContent || !elementText || !implementationType) {
      return res.status(400).json({
        error: 'HTML content, element text, and implementation type are required'
      });
    }

    // Set SSE headers with proper CORS for credentials
    const origin = req.headers.origin || 'https://app.justprototype.dev';
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization'
    });

    // Get user information for version tracking
    const userId = req.user?.dbId || req.user?.id;

    await llmServiceV3.implementFeature(
      htmlContent,
      elementText,
      elementType,
      implementationType,
      res,
      conversationHistory || [],
      intentData,
      { projectId: req.body.projectId, userId }
    );
  } catch (error) {
    console.error('Error in implementFeature:', error);
    next(error);
  }
}

/**
 * Generate complete HTML from prompt
 */
async function generateHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, projectId, pageTitle } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Get user information for session creation
    const userId = req.user?.dbId || req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    console.log('🎯 [Controller] generateHTML received request');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🏗️ Project ID:', projectId);
    console.log('👤 User ID:', userId);
    console.log('📄 Page Title:', pageTitle);
    console.log('🔍 Contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));
    console.log('🔍 Contains sections:', prompt.includes('Implementation Requirements'));
    console.log('🔍 Contains features:', prompt.includes('Key Features'));

    // Set SSE headers with proper CORS for credentials
    const origin = req.headers.origin || 'https://app.justprototype.dev';
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization'
    });

    await llmServiceV3.generateHTML(prompt, res, null, {
      projectId,
      userId,
      pageTitle
    });
  } catch (error) {
    console.error('Error in generateHTML:', error);
    next(error);
  }
}

/**
 * Edit existing HTML with targeted changes (Readdy.ai style)
 * Enhanced with prototyping context for demo-ready functionality
 */
async function editHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const {
      htmlContent,
      prompt,
      elementSelector,
      conversationHistory,
      fragmentHtml,
      implementationType,
      sessionId,
      pageId,
      elementContext,
      targetPosition
    } = req.body;

    console.log('🎯 [Controller] editHTML received request');
    console.log('📏 HTML content length:', htmlContent?.length || 0);
    console.log('📏 Fragment HTML length:', fragmentHtml?.length || 0);
    console.log('📏 Prompt length:', prompt?.length || 0);
    console.log('🔧 Element selector:', elementSelector);
    console.log('🔧 Implementation type:', implementationType);
    console.log('🔧 Session ID:', sessionId);
    console.log('🔧 Page ID:', pageId);
    console.log('🔧 Element context:', elementContext ? 'provided' : 'none');
    console.log('🔧 Target position:', targetPosition);
    console.log('📝 Conversation history length:', conversationHistory?.length || 0);

    // 🚨 DEBUGGING: Check if this is a free text prompt without element selection
    const hasElementSelection = !!(elementSelector || fragmentHtml || elementContext);
    console.log('🎯 [Controller] Has element selection:', hasElementSelection);
    console.log('🎯 [Controller] Request type:', hasElementSelection ? 'TARGETED_EDIT' : 'FREE_TEXT_PROMPT');

    // 🚀 OPTIMIZATION: Try to get HTML from database first
    let finalHtmlContent = htmlContent;
    let finalFragmentHtml = fragmentHtml;

    if (pageId) {
      console.log('🔍 [Optimization] pageId provided, attempting database retrieval...');
      console.log('🔍 [Optimization] pageId:', pageId, 'userId:', req.user?.dbId || req.user?.id);

      // 🚨 VALIDATION: Check if pageId is a valid integer
      const pageIdInt = parseInt(pageId);
      if (isNaN(pageIdInt) || pageIdInt.toString() !== pageId.toString()) {
        console.log('⚠️ [Optimization] Invalid pageId format:', pageId, '- must be a valid integer');
        console.log('🔄 [Optimization] Skipping database lookup due to invalid pageId');
      } else {
        try {
          const prototypePageService = require('../services/prototypePageService');
          const userId = req.user?.dbId || req.user?.id;
          const page = await prototypePageService.getPageById(pageIdInt, userId);

        console.log('🔍 [Optimization] Database query result:', page ? 'FOUND' : 'NOT_FOUND');
        if (page) {
          console.log('🔍 [Optimization] Page data:', {
            id: page.id,
            title: page.title,
            html_content_length: page.html_content?.length || 0,
            user_id: page.user_id,
            prototype_id: page.prototype_id,
            created_at: page.created_at
          });
          console.log('🔍 [Optimization] HTML content preview:', page.html_content?.substring(0, 200) + '...');
        } else {
          console.log('🔍 [Optimization] Page not found - possible reasons:');
          console.log('  - Page ID does not exist in database');
          console.log('  - User ID mismatch (page belongs to different user)');
          console.log('  - Database connection issue');

          // 🚨 DEBUG: Let's check what pages exist for this user and try fallback
          try {
            // Get the project ID from the request body
            const projectId = req.body.projectId;
            console.log('🔍 [DEBUG] Looking for pages in project:', projectId);

            const allPages = await prototypePageService.getPagesByPrototype(projectId, userId);
            console.log('🔍 [DEBUG] Available pages for user', userId, 'in prototype', projectId + ':');
            allPages.forEach(p => {
              console.log(`  - Page ID: ${p.id}, Title: "${p.title}", HTML Length: ${p.html_content?.length || 0}, Created: ${p.created_at}`);
            });

            // 🚨 FALLBACK: If requested page doesn't exist, try to use the most recent page
            if (allPages.length > 0) {
              const mostRecentPage = allPages[0]; // Pages are usually ordered by creation date desc
              console.log(`🔄 [FALLBACK] Using most recent page as fallback: ID ${mostRecentPage.id} ("${mostRecentPage.title}")`);

              if (mostRecentPage.html_content) {
                finalHtmlContent = mostRecentPage.html_content;
                console.log('✅ [FALLBACK] Retrieved HTML from fallback page:', finalHtmlContent.length, 'chars');
                console.log('📊 [FALLBACK] This should allow the edit to proceed');
              } else {
                console.log('⚠️ [FALLBACK] Most recent page has no HTML content');
              }
            } else {
              console.log('⚠️ [DEBUG] No pages found for this project');
            }
          } catch (debugError) {
            console.log('🔍 [DEBUG] Failed to fetch available pages:', debugError.message);
          }
        }

        if (page && page.html_content) {
          finalHtmlContent = page.html_content;
          const bandwidthSaved = (htmlContent?.length || 0) + (fragmentHtml?.length || 0);
          console.log('✅ [Optimization] Retrieved HTML from database:', finalHtmlContent.length, 'chars');
          console.log('💾 [Optimization] Bandwidth saved:', bandwidthSaved, 'bytes');
          console.log('📊 [Optimization] Efficiency:', bandwidthSaved > 0 ? 'OPTIMIZED' : 'FALLBACK');
        } else {
          console.log('⚠️ [Optimization] Page not found in database or no HTML content, using provided HTML');
        }
        } catch (error) {
          console.error('❌ [Optimization] Database lookup failed:', error);
          console.log('🔄 [Optimization] Falling back to provided HTML content');
        }
      }
    } else {
      console.log('📝 [Standard] No pageId provided, using provided HTML content');
    }

    console.log('🔍 [Validation] Final content check:');
    console.log('🔍 [Validation] finalHtmlContent length:', finalHtmlContent?.length || 0);
    console.log('🔍 [Validation] finalFragmentHtml length:', finalFragmentHtml?.length || 0);
    console.log('🔍 [Validation] prompt length:', prompt?.length || 0);

    if ((!finalHtmlContent && !finalFragmentHtml) || !prompt) {
      console.error('❌ [Validation] Missing required content or prompt');
      return res.status(400).json({
        error: 'HTML content (or pageId for database lookup) and prompt are required'
      });
    }

    console.log('✅ [Validation] All required content available, proceeding with edit...');

    // 🚨 ENHANCED: For free text prompts, ensure we have HTML content for context
    if (!hasElementSelection && finalHtmlContent) {
      console.log('🎯 [Free Text Enhancement] Adding page context for free text prompt');
      console.log('📄 [Free Text Enhancement] Using page HTML as context:', finalHtmlContent.length, 'chars');
    }

    // Add prototyping context to enhance prompt robustness
    const prototypingContext = `
PROTOTYPING CONTEXT: This is for live client demonstration
DEMO REQUIREMENTS: All functionality must work immediately
QUALITY STANDARD: Professional, polished, stakeholder-ready
ERROR PREVENTION: Include complete JavaScript functions with error handling
`;

    // Set SSE headers with proper CORS for credentials
    const origin = req.headers.origin || 'https://app.justprototype.dev';
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization'
    });

    // Enhanced prompt with prototyping requirements
    const enhancedPrompt = prototypingContext + prompt;

    // Get user information for version tracking
    const userId = req.user?.dbId || req.user?.id;

    await llmServiceV3.editHTML(
      finalHtmlContent,
      enhancedPrompt,
      res,
      null,
      elementSelector,
      conversationHistory || [],
      {
        projectId: req.body.projectId,
        userId,
        fragmentHtml: finalFragmentHtml,
        pageId: sessionId || pageId,
        implementationType,
        elementContext,
        targetPosition,
        // 🚨 CRITICAL FIX: Set isFragmentEdit flag when fragment is available
        isFragmentEdit: !!(finalFragmentHtml && elementSelector)
      }
    );
  } catch (error) {
    console.error('Error in editHTML:', error);
    next(error);
  }
}

/**
 * NEW: Enhance user prompt with detailed instructions
 */
async function enhancePrompt(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, htmlContext, elementContext, pageId, elementSelector, fragmentHtml } = req.body;

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      });
    }

    console.log('🔧 [Controller] enhancePrompt received request');
    console.log('📝 Original prompt:', prompt);
    console.log('📏 HTML context length:', htmlContext?.length || 0);
    console.log('📏 Element context length:', elementContext?.length || 0);
    console.log('📏 Fragment HTML length:', fragmentHtml?.length || 0);
    console.log('🔧 Page ID:', pageId);
    console.log('🔧 Element selector:', elementSelector);

    // 🚀 OPTIMIZATION: Try to get HTML context from database first
    let finalHtmlContext = htmlContext;
    let finalElementContext = elementContext;

    if (pageId && !htmlContext) {
      console.log('🔍 [Enhancement Optimization] Attempting to retrieve HTML from database...');

      // 🚨 VALIDATION: Check if pageId is a valid integer
      const pageIdInt = parseInt(pageId);
      if (isNaN(pageIdInt) || pageIdInt.toString() !== pageId.toString()) {
        console.log('⚠️ [Enhancement Optimization] Invalid pageId format:', pageId, '- must be a valid integer');
        console.log('🔄 [Enhancement Optimization] Skipping database lookup due to invalid pageId');
      } else {
        try {
          const prototypePageService = require('../services/prototypePageService');
          const userId = req.user?.dbId || req.user?.id;
          const page = await prototypePageService.getPageById(pageIdInt, userId);

        if (page && page.html_content) {
          // Only take first 2000 chars for context (enhancement doesn't need full HTML)
          finalHtmlContext = page.html_content.substring(0, 2000);
          console.log('✅ [Enhancement Optimization] Retrieved HTML context from database:', finalHtmlContext.length, 'chars');
          console.log('💾 [Enhancement Optimization] Bandwidth saved:', htmlContext?.length || 0, 'bytes');
        } else {
          console.log('⚠️ [Enhancement Optimization] Page not found in database, using provided context');
        }
        } catch (error) {
          console.error('❌ [Enhancement Optimization] Database lookup failed:', error);
          console.log('🔄 [Enhancement Optimization] Falling back to provided HTML context');
        }
      }
    }

    // Use fragment HTML for element context if provided
    if (fragmentHtml && !finalElementContext) {
      finalElementContext = fragmentHtml.substring(0, 500); // Smaller fragment for context
      console.log('🎯 [Enhancement] Using fragment HTML as element context:', finalElementContext.length, 'chars');
    }

    const result = await llmServiceV3.enhancePrompt(
      prompt,
      finalHtmlContext || '',
      finalElementContext || '',
      elementSelector
    );

    if (result.success) {
      res.json({
        success: true,
        enhancement: result.enhancement,
        originalPrompt: prompt
      });
    } else {
      res.status(400).json({
        error: result.error || 'Failed to enhance prompt'
      });
    }
  } catch (error) {
    console.error('Error in enhancePrompt:', error);
    next(error);
  }
}

/**
 * Step 1: Generate Intent from element click (like Readdy's /api/page_gen/generate_intent)
 */
async function generateIntent(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { elementCode, htmlContent, conversationHistory } = req.body;
    if (!elementCode || !htmlContent) {
      return res.status(400).json({
        error: 'Element code and HTML content are required'
      });
    }

    const result = await llmServiceV3.generateIntent(
      elementCode,
      htmlContent,
      conversationHistory || []
    );

    if (result.success) {
      res.json({
        code: 'OK',
        data: result.intent,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: '',
          detail: null
        }
      });
    } else {
      res.status(500).json({
        code: 'ERROR',
        error: result.error,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: 'Failed to generate intent',
          detail: result.error
        }
      });
    }
  } catch (error) {
    console.error('Error in generateIntent:', error);
    next(error);
  }
}

/**
 * Generate structured plan from prompt (for plan review page)
 */
async function generatePlan(req, res, next) {
   console.log('In generatePlan');
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, deviceType } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const result = await llmServiceV3.generateStructuredPlan(prompt, deviceType);

    if (result.success) {
      res.json({ plan: result.plan });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error in generatePlan:', error);
    next(error);
  }
}

/**
 * Generate streaming plan (for chat)
 */
async function generateStreamingPlan(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Set SSE headers with proper CORS for credentials
    const origin = req.headers.origin || 'https://app.justprototype.dev';
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization'
    });

    await llmServiceV3.generatePlan(prompt, res);
  } catch (error) {
    console.error('Error in generateStreamingPlan:', error);
    next(error);
  }
}

/**
 * Generate code from plan
 */
async function generateCode(req, res, next) {
  try {
    // Throw not implemented error
    return res.status(501).json({
      error: 'Not implemented',
      message: 'generateCode functionality is not yet implemented'
    });
  } catch (error) {
    console.error('Error in generateCode:', error);
    next(error);
  }
}

module.exports = {
  generateIntent,
  implementFeature,
  generateHTML,
  editHTML,
  enhancePrompt,
  generatePlan,
  generateStreamingPlan,
  generateCode
};
